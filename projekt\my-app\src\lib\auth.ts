import jwt from "jsonwebtoken"
import { prisma } from "./prisma"

const JWT_SECRET = process.env.NEXTAUTH_SECRET || "your-secret-key"

export interface AuthUser {
  id: string
  email: string
}

export function generateMagicToken(email: string): string {
  return jwt.sign({ email }, JWT_SECRET, { expiresIn: "15m" })
}

export function verifyMagicToken(token: string): { email: string } | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { email: string }
    return decoded
  } catch {
    return null
  }
}

export function generateSessionToken(userId: string): string {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: "7d" })
}

export function verifySessionToken(token: string): { userId: string } | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string }
    return decoded
  } catch {
    return null
  }
}

export async function getCurrentUser(sessionToken?: string): Promise<AuthUser | null> {
  if (!sessionToken) return null

  const decoded = verifySessionToken(sessionToken)
  if (!decoded) return null

  const user = await prisma.user.findUnique({
    where: { id: decoded.userId },
    select: { id: true, email: true },
  })

  return user
}
