# Environment
NODE_ENV=production

# Authentication - ZMĚŇTE V PRODUKCI!
NEXTAUTH_SECRET=your-very-secure-secret-key-change-this-in-production

# App URL - ZMĚŇTE NA VAŠI DOMÉNU!
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database (Docker PostgreSQL)
DATABASE_URL=********************************************/rfp_radar

# Redis (Docker Redis)
REDIS_URL=redis://redis:6379

# Email (Postmark) - dummy for local development
POSTMARK_TOKEN=dummy-postmark-token

# AI Provider - dummy for local development
AI_PROVIDER=openai
OPENAI_API_KEY=dummy-openai-key

# Alternative AI providers
# AI_PROVIDER=azure
# AZURE_OPENAI_API_KEY=your-azure-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com

# AI_PROVIDER=openrouter
# OPENROUTER_API_KEY=your-openrouter-key

# Feature flags
BETA_LIMIT_WATCHLISTS=2
MAX_ITEMS_PER_DIGEST=10

# Timezone
APP_TIMEZONE=Europe/Prague
DIGEST_LOCAL_TIME=07:30

# Logging
LOG_LEVEL=info
