# Environment
NODE_ENV=development

# Authentication
NEXTAUTH_SECRET=local-development-secret-key-123

# App URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database - we'll use a free PostgreSQL from Neon
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Redis (we'll skip Redis for now)
# REDIS_URL=redis://localhost:6379

# Email (Postmark) - dummy for local development
POSTMARK_TOKEN=dummy-postmark-token

# AI Provider - dummy for local development
AI_PROVIDER=openai
OPENAI_API_KEY=dummy-openai-key

# Alternative AI providers
# AI_PROVIDER=azure
# AZURE_OPENAI_API_KEY=your-azure-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com

# AI_PROVIDER=openrouter
# OPENROUTER_API_KEY=your-openrouter-key

# Feature flags
BETA_LIMIT_WATCHLISTS=2
MAX_ITEMS_PER_DIGEST=10

# Timezone
APP_TIMEZONE=Europe/Prague
DIGEST_LOCAL_TIME=07:30

# Logging
LOG_LEVEL=info
