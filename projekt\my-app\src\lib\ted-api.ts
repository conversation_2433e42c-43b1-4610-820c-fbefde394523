import { logger } from "./logger"

export interface TEDSearchParams {
  cpv?: string[]
  keywords?: string[]
  countryCodes?: string[]
  languages?: string[]
  publishedAfter?: Date
  limit?: number
  offset?: number
}

export interface TEDNotice {
  notice_id: string
  publication_number: string
  title: string
  buyer_name?: string
  buyer_country?: string
  language?: string
  cpv_main?: string
  deadline?: string
  publication_date: string
  url: string
  description?: string
  [key: string]: any
}

export interface TEDSearchResponse {
  notices: TEDNotice[]
  total: number
  hasMore: boolean
  nextOffset?: number
}

export function buildExpertQuery(params: TEDSearchParams): string {
  const conditions: string[] = []

  // CPV codes
  if (params.cpv && params.cpv.length > 0) {
    const cpvConditions = params.cpv.map((code) => `CPV=${code}*`).join(" OR ")
    conditions.push(`(${cpvConditions})`)
  }

  // Keywords in title or description
  if (params.keywords && params.keywords.length > 0) {
    const keywordConditions = params.keywords.map((keyword) => `(TI="${keyword}" OR TD="${keyword}")`).join(" OR ")
    conditions.push(`(${keywordConditions})`)
  }

  // Country codes
  if (params.countryCodes && params.countryCodes.length > 0) {
    const countryConditions = params.countryCodes.map((code) => `CY=${code}`).join(" OR ")
    conditions.push(`(${countryConditions})`)
  }

  // Languages
  if (params.languages && params.languages.length > 0) {
    const langConditions = params.languages.map((lang) => `LG=${lang}`).join(" OR ")
    conditions.push(`(${langConditions})`)
  }

  // Publication date
  if (params.publishedAfter) {
    const dateStr = params.publishedAfter.toISOString().split("T")[0]
    conditions.push(`PD>=${dateStr}`)
  }

  return conditions.join(" AND ")
}

export async function searchTEDNotices(params: TEDSearchParams): Promise<TEDSearchResponse> {
  const expertQuery = buildExpertQuery(params)

  const requestBody = {
    query: expertQuery,
    pageSize: params.limit || 100,
    pageNum: Math.floor((params.offset || 0) / (params.limit || 100)) + 1,
    sortField: "PD",
    sortOrder: "DESC",
  }

  logger.info("TED API request", { expertQuery, requestBody })

  try {
    const response = await fetch("https://ted.europa.eu/api/v3/notices/search", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      throw new Error(`TED API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    // Transform the response to our format
    const notices: TEDNotice[] = (data.results || []).map((item: any) => ({
      notice_id: item.ND || item.publicationNumber || `ted-${Date.now()}-${Math.random()}`,
      publication_number: item.publicationNumber || item.ND,
      title: item.TI || item.title || "Untitled Notice",
      buyer_name: item.AA || item.buyerName,
      buyer_country: item.CY || item.country,
      language: item.LG || item.language,
      cpv_main: item.CPV || item.cpvMain,
      deadline: item.TD || item.deadline,
      publication_date: item.PD || item.publicationDate || new Date().toISOString(),
      url: item.NU || `https://ted.europa.eu/udl?uri=TED:NOTICE:${item.ND || item.publicationNumber}`,
      description: item.DS || item.description,
      raw: item,
    }))

    return {
      notices,
      total: data.total || notices.length,
      hasMore: data.hasMore || false,
      nextOffset: data.hasMore ? (params.offset || 0) + notices.length : undefined,
    }
  } catch (error) {
    logger.error("TED API error", { error: error.message, params })

    // Return mock data for development
    if (process.env.NODE_ENV === "development") {
      return {
        notices: [
          {
            notice_id: `mock-${Date.now()}-1`,
            publication_number: `2024/S 001-000001`,
            title: "Software Development Services for Digital Platform",
            buyer_name: "Ministry of Digital Affairs",
            buyer_country: "CZ",
            language: "cs",
            cpv_main: "72000000",
            deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            publication_date: new Date().toISOString(),
            url: "https://ted.europa.eu/udl?uri=TED:NOTICE:mock-1",
            description: "Development of comprehensive digital platform for government services.",
          },
          {
            notice_id: `mock-${Date.now()}-2`,
            publication_number: `2024/S 001-000002`,
            title: "Cloud Infrastructure and Hosting Services",
            buyer_name: "Prague City Council",
            buyer_country: "CZ",
            language: "cs",
            cpv_main: "72000000",
            deadline: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
            publication_date: new Date().toISOString(),
            url: "https://ted.europa.eu/udl?uri=TED:NOTICE:mock-2",
            description: "Cloud infrastructure services for municipal IT systems.",
          },
        ],
        total: 2,
        hasMore: false,
      }
    }

    throw error
  }
}
