import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Bell, Zap } from "lucide-react"

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="border-b bg-white">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Search className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold">RFP Radar</span>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/login">
              <Button variant="outline">Přihlásit se</Button>
            </Link>
            <Link href="/dashboard">
              <Button>Demo Dashboard</Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="container mx-auto px-4 text-center">
          <Badge className="mb-4" variant="secondary">
            Beta verze - Demo
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Monitoring veřejných zakázek
            <span className="text-blue-600"> s AI</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Automaticky sledujte evropské veřejné zakázky, získejte AI shrnutí relevantních příležitostí a dostávejte
            denní e-mailové přehledy přímo do vaší schránky.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/dashboard">
              <Button size="lg" className="text-lg px-8">
                Zobrazit Demo
              </Button>
            </Link>
            <Link href="/watchlists/new">
              <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent">
                Vytvořit watchlist
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Jak RFP Radar funguje</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Jednoduché nastavení, automatické sledování, inteligentní shrnutí
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Search className="w-6 h-6 text-blue-600" />
                </div>
                <CardTitle>Nastavte watchlisty</CardTitle>
                <CardDescription>
                  Definujte klíčová slova, CPV kódy, země a jazyky pro sledování relevantních zakázek
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="w-6 h-6 text-green-600" />
                </div>
                <CardTitle>AI analýza</CardTitle>
                <CardDescription>
                  Každá relevantní zakázka je automaticky analyzována AI pro rychlé pochopení příležitosti
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <Bell className="w-6 h-6 text-purple-600" />
                </div>
                <CardTitle>Denní přehledy</CardTitle>
                <CardDescription>
                  Dostávejte každé ráno e-mail s nejrelevantnějšími zakázkami a jejich AI shrnutím
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Proč RFP Radar?</h2>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Oficiální zdroje dat</h3>
                    <p className="text-gray-600">
                      Čerpáme pouze z oficiálního TED Search API, žádné scrapování nebo neoficiální zdroje
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Transparentní AI</h3>
                    <p className="text-gray-600">
                      Všechna AI shrnutí jsou jasně označena s doporučením ověřit si informace v originále
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Nízké náklady</h3>
                    <p className="text-gray-600">
                      Optimalizované pro efektivitu s inteligentním cachováním a limity na AI použití
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-lg">
              <h3 className="text-xl font-semibold mb-4">Ukázka AI shrnutí</h3>
              <div className="border-l-4 border-blue-500 pl-4 mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  <strong>Zadavatel:</strong> Ministerstvo pro místní rozvoj ČR
                  <br />
                  <strong>CPV:</strong> 72000000 - IT služby
                  <br />
                  <strong>Deadline:</strong> 15.12.2024
                </p>
                <p className="text-sm text-gray-700">
                  Poptávka na vývoj digitální platformy pro správu dotačních programů. Požadována zkušenost s velkými
                  státními systémy, certifikace ISO 27001. Hodnocení 60% cena, 40% kvalita...
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Eligibility: ANO
                </Badge>
                <span className="text-xs text-gray-500">AI shrnutí, ověřte v originálu</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Začněte sledovat zakázky ještě dnes</h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Nastavení trvá méně než 2 minuty. Žádné dlouhé smlouvy, zrušit můžete kdykoli.
          </p>
          <Link href="/dashboard">
            <Button size="lg" className="text-lg px-8">
              Zobrazit Demo Dashboard
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Search className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">RFP Radar</span>
              </div>
              <p className="text-gray-400">Monitoring veřejných zakázek s AI</p>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Produkt</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/dashboard" className="hover:text-white">
                    Demo
                  </Link>
                </li>
                <li>
                  <Link href="/watchlists/new" className="hover:text-white">
                    Watchlisty
                  </Link>
                </li>
                <li>
                  <Link href="/notices" className="hover:text-white">
                    Zakázky
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Podpora</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white">
                    Nápověda
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Kontakt
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Status
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Právní</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white">
                    Ochrana údajů
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Podmínky
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 RFP Radar. Všechna práva vyhrazena.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
