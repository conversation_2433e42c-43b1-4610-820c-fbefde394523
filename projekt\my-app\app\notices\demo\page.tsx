import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Search, ExternalLink, Calendar, MapPin, Building, FileText } from "lucide-react"

// Mock notice data
const mockNotice = {
  id: "demo",
  title: "Software Development Services for Digital Transformation Platform",
  buyer: "Ministry of Digital Affairs, Czech Republic",
  country: "CZ",
  language: "cs",
  cpvMain: "72000000",
  deadline: "2024-12-15T23:59:59Z",
  publicationDate: "2024-11-01T08:00:00Z",
  url: "https://ted.europa.eu/udl?uri=TED:NOTICE:demo-2024",
  description:
    "The Ministry of Digital Affairs seeks a comprehensive software development partner for creating a next-generation digital transformation platform. This platform will serve as the backbone for modernizing government services and improving citizen experience through digital channels.",
  brief: {
    summary: `**Zadavatel:** Ministerstvo pro místní rozvoj ČR
**Předmět:** Vývoj komplexní digitální platformy pro transformaci veřejných služeb
**CPV:** 72000000 - IT služby
**Deadline:** 15.12.2024
**Místo plnění:** Praha, Česká republika

Ministerstvo poptává partnera pro vývoj rozsáhlé digitální platformy, která bude sloužit jako páteř pro modernizaci státních služeb. Platforma má zlepšit uživatelskou zkušenost občanů prostřednictvím digitálních kanálů.

**Kvalifikační požadavky:**
- Minimálně 5 let zkušeností s velkými státními IT projekty
- Certifikace ISO 27001 pro bezpečnost informací
- Reference na podobné projekty v hodnotě min. 50 mil. Kč
- Tým min. 20 vývojářů s bezpečnostním prověřením

**Hodnoticí kritéria:** 60% cena, 40% kvality a technické řešení
**Forma řízení:** Otevřené řízení dle zákona o veřejných zakázkách
**Jazyk nabídky:** Čeština`,
    eligibility: "YES" as const,
    blockers: [],
    confidence: 0.9,
  },
}

export default function NoticeDetailPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/dashboard" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Search className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold">RFP Radar</span>
          </Link>

          <div className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                Zpět na Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Notice Header */}
        <div className="mb-8">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{mockNotice.title}</h1>
              <div className="flex items-center space-x-4 text-gray-600">
                <div className="flex items-center space-x-1">
                  <Building className="w-4 h-4" />
                  <span>{mockNotice.buyer}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MapPin className="w-4 h-4" />
                  <span>{mockNotice.country}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>Publikováno: {new Date(mockNotice.publicationDate).toLocaleDateString("cs-CZ")}</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col items-end space-y-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                Eligibility: {mockNotice.brief.eligibility === "YES" ? "ANO" : mockNotice.brief.eligibility}
              </Badge>
              <a
                href={mockNotice.url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-1 text-blue-600 hover:underline"
              >
                <ExternalLink className="w-4 h-4" />
                <span>Zobrazit originál</span>
              </a>
            </div>
          </div>

          {/* Key Info */}
          <div className="grid md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardContent className="pt-4">
                <div className="text-sm text-gray-600">CPV kód</div>
                <div className="font-semibold">{mockNotice.cpvMain}</div>
                <div className="text-xs text-gray-500">IT služby</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-4">
                <div className="text-sm text-gray-600">Deadline</div>
                <div className="font-semibold text-red-600">
                  {new Date(mockNotice.deadline).toLocaleDateString("cs-CZ")}
                </div>
                <div className="text-xs text-gray-500">
                  {Math.ceil((new Date(mockNotice.deadline).getTime() - Date.now()) / (1000 * 60 * 60 * 24))} dní zbývá
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-4">
                <div className="text-sm text-gray-600">Jazyk</div>
                <div className="font-semibold">{mockNotice.language.toUpperCase()}</div>
                <div className="text-xs text-gray-500">Čeština</div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* AI Brief */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="w-5 h-5" />
                    <span>AI Shrnutí</span>
                  </CardTitle>
                  <Badge variant="outline" className="text-xs">
                    Confidence: {(mockNotice.brief.confidence * 100).toFixed(0)}%
                  </Badge>
                </div>
                <CardDescription>Automaticky generované shrnutí zakázky pomocí AI</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  <div className="whitespace-pre-line text-gray-700">{mockNotice.brief.summary}</div>
                </div>

                {mockNotice.brief.eligibility === "YES" && (
                  <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="font-semibold text-green-800">Eligibility: ANO</span>
                    </div>
                    <p className="text-green-700 text-sm">
                      Zakázka splňuje základní kritéria pro účast. Doporučujeme detailní prostudování zadávací
                      dokumentace.
                    </p>
                  </div>
                )}

                {mockNotice.brief.blockers.length > 0 && (
                  <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="font-semibold text-red-800 mb-2">Možné překážky:</div>
                    <ul className="list-disc list-inside text-red-700 text-sm space-y-1">
                      {mockNotice.brief.blockers.map((blocker, index) => (
                        <li key={index}>{blocker}</li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-yellow-800 text-sm">
                    <strong>Upozornění:</strong> Toto je AI shrnutí. Vždy si ověřte všechny informace v originální
                    zadávací dokumentaci.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Akce</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <a href={mockNotice.url} target="_blank" rel="noopener noreferrer">
                  <Button className="w-full">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Zobrazit v TED
                  </Button>
                </a>
                <Button variant="outline" className="w-full bg-transparent">
                  <Calendar className="w-4 h-4 mr-2" />
                  Přidat do kalendáře
                </Button>
                <Button variant="outline" className="w-full bg-transparent">
                  Sdílet zakázku
                </Button>
              </CardContent>
            </Card>

            {/* Technical Details */}
            <Card>
              <CardHeader>
                <CardTitle>Technické údaje</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <div className="text-sm text-gray-600">Notice ID</div>
                  <div className="font-mono text-sm">{mockNotice.id}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Publikace</div>
                  <div className="text-sm">{new Date(mockNotice.publicationDate).toLocaleString("cs-CZ")}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Deadline</div>
                  <div className="text-sm">{new Date(mockNotice.deadline).toLocaleString("cs-CZ")}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Země</div>
                  <div className="text-sm">{mockNotice.country}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Jazyk</div>
                  <div className="text-sm">{mockNotice.language}</div>
                </div>
              </CardContent>
            </Card>

            {/* Related */}
            <Card>
              <CardHeader>
                <CardTitle>Související zakázky</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-sm">
                    <Link href="#" className="text-blue-600 hover:underline">
                      Healthcare IT System Modernization
                    </Link>
                    <div className="text-xs text-gray-500">Ministry of Health • CPV: 72000000</div>
                  </div>
                  <div className="text-sm">
                    <Link href="#" className="text-blue-600 hover:underline">
                      Municipal Digital Services Platform
                    </Link>
                    <div className="text-xs text-gray-500">Prague City Council • CPV: 72000000</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Original Description */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Originální popis</CardTitle>
            <CardDescription>Popis zakázky z TED databáze</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-gray-700">{mockNotice.description}</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
