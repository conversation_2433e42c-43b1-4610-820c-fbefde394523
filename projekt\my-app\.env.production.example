# PRODUKČNÍ KONFIGURACE - PŘÍKLAD
# DŮLEŽITÉ: Zkopírujte do .env.production a změňte všechny hodnoty!

# Environment
NODE_ENV=production

# Authentication - ZMĚŇTE NA SILNÉ HESLO!
# Vygenerujte: openssl rand -base64 32
NEXTAUTH_SECRET=your-very-secure-secret-key-change-this-in-production-123456789

# App URL - ZMĚŇTE NA VAŠI DOMÉNU!
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Database
# Docker: ********************************************/rfp_radar
# Supabase: postgresql://postgres:[password]@db.[project].supabase.co:5432/postgres
# Neon: postgresql://[user]:[password]@[host]/[dbname]?sslmode=require
DATABASE_URL=********************************************/rfp_radar

# Redis
# Docker: redis://redis:6379
# Upstash: redis://:[password]@[host]:[port]
REDIS_URL=redis://redis:6379

# Email (Postmark) - ZÍSKEJTE SKUTEČNÝ TOKEN!
# Registrace: https://postmarkapp.com/
POSTMARK_TOKEN=your-postmark-server-token

# AI Provider - ZÍSKEJTE SKUTEČNÝ API KLÍČ!
# OpenAI: https://platform.openai.com/api-keys
AI_PROVIDER=openai
OPENAI_API_KEY=sk-your-openai-api-key

# Alternative AI providers
# AI_PROVIDER=azure
# AZURE_OPENAI_API_KEY=your-azure-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com

# AI_PROVIDER=openrouter
# OPENROUTER_API_KEY=your-openrouter-key

# Feature flags
BETA_LIMIT_WATCHLISTS=2
MAX_ITEMS_PER_DIGEST=10

# Timezone
APP_TIMEZONE=Europe/Prague
DIGEST_LOCAL_TIME=07:30

# Logging
LOG_LEVEL=info
