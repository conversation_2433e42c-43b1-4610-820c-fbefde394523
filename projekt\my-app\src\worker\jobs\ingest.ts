import { prisma } from "../../lib/prisma"
import { searchTEDNotices, type TEDSearchParams } from "../../lib/ted-api"
import { calculateRelevanceScore } from "../../lib/scoring"
import { logger } from "../../lib/logger"
import { createHash } from "crypto"
import { subDays } from "date-fns"

export async function ingestTEDNotices(): Promise<void> {
  logger.info("Starting TED notices ingest")

  try {
    // Get last processed date from job state
    const lastProcessed = await prisma.jobState.findUnique({
      where: { key: "ted-ingest-last-processed" },
    })

    const publishedAfter = lastProcessed?.value ? new Date(lastProcessed.value as string) : subDays(new Date(), 2) // Default: last 2 days

    // Get all active watchlists to build search parameters
    const watchlists = await prisma.watchlist.findMany({
      where: { isActive: true },
      include: { user: true },
    })

    if (watchlists.length === 0) {
      logger.info("No active watchlists found, skipping ingest")
      return
    }

    // Aggregate search parameters from all watchlists
    const allKeywords = new Set<string>()
    const allCpvCodes = new Set<string>()
    const allCountries = new Set<string>()
    const allLanguages = new Set<string>()

    watchlists.forEach((watchlist) => {
      watchlist.keywords.forEach((k) => allKeywords.add(k))
      watchlist.cpvCodes.forEach((c) => allCpvCodes.add(c))
      watchlist.countries.forEach((c) => allCountries.add(c))
      watchlist.languages.forEach((l) => allLanguages.add(l))
    })

    const searchParams: TEDSearchParams = {
      keywords: Array.from(allKeywords),
      cpv: Array.from(allCpvCodes),
      countryCodes: Array.from(allCountries),
      languages: Array.from(allLanguages),
      publishedAfter,
      limit: 100,
    }

    logger.info("Searching TED notices", { searchParams, watchlistCount: watchlists.length })

    let totalProcessed = 0
    let offset = 0
    let hasMore = true

    while (hasMore) {
      const response = await searchTEDNotices({ ...searchParams, offset })

      for (const tedNotice of response.notices) {
        // Create hash for deduplication
        const hash = createHash("sha256").update(JSON.stringify(tedNotice)).digest("hex")

        // Check if notice already exists
        const existing = await prisma.notice.findUnique({
          where: { noticeUid: tedNotice.notice_id },
        })

        if (existing) {
          logger.debug("Notice already exists, skipping", { noticeUid: tedNotice.notice_id })
          continue
        }

        // Create notice
        const notice = await prisma.notice.create({
          data: {
            noticeUid: tedNotice.notice_id,
            raw: tedNotice,
            title: tedNotice.title,
            buyerName: tedNotice.buyer_name,
            country: tedNotice.buyer_country,
            language: tedNotice.language,
            cpvMain: tedNotice.cpv_main,
            deadline: tedNotice.deadline ? new Date(tedNotice.deadline) : null,
            publicationDate: new Date(tedNotice.publication_date),
            url: tedNotice.url,
            hash,
          },
        })

        // Calculate relevance scores for each watchlist
        for (const watchlist of watchlists) {
          const relevanceScore = calculateRelevanceScore({
            noticeTitle: notice.title,
            noticeDescription: tedNotice.description,
            noticeCpv: notice.cpvMain,
            noticeCountry: notice.country,
            noticeLanguage: notice.language,
            publicationDate: notice.publicationDate,
            watchlistKeywords: watchlist.keywords,
            watchlistCpvCodes: watchlist.cpvCodes,
            watchlistCountries: watchlist.countries,
            watchlistLanguages: watchlist.languages,
          })

          // Only create match if score meets minimum threshold
          if (relevanceScore >= watchlist.minScore) {
            await prisma.match.create({
              data: {
                watchlistId: watchlist.id,
                noticeId: notice.id,
                relevanceScore,
              },
            })

            logger.debug("Match created", {
              watchlistId: watchlist.id,
              noticeId: notice.id,
              relevanceScore,
            })
          }
        }

        totalProcessed++
      }

      hasMore = response.hasMore
      offset = response.nextOffset || offset + response.notices.length

      if (response.notices.length === 0) {
        hasMore = false
      }
    }

    // Update last processed timestamp
    await prisma.jobState.upsert({
      where: { key: "ted-ingest-last-processed" },
      update: { value: new Date().toISOString() },
      create: {
        key: "ted-ingest-last-processed",
        value: new Date().toISOString(),
      },
    })

    // Log audit entry
    await prisma.auditLog.create({
      data: {
        level: "info",
        src: "ingest-worker",
        message: "TED ingest completed",
        meta: {
          totalProcessed,
          watchlistCount: watchlists.length,
          searchParams,
        },
      },
    })

    logger.info("TED ingest completed", { totalProcessed })
  } catch (error) {
    logger.error("TED ingest failed", { error: error.message })

    await prisma.auditLog.create({
      data: {
        level: "error",
        src: "ingest-worker",
        message: "TED ingest failed",
        meta: { error: error.message },
      },
    })

    throw error
  }
}
