import { Worker, Queue } from "bullmq"
import IORedis from "ioredis"
import { logger } from "../lib/logger"
import { ingestTEDNotices } from "./jobs/ingest"
import { generateBriefs } from "./jobs/brief"
import { sendDigests } from "./jobs/digest"

const redis = new IORedis(process.env.REDIS_URL || "redis://localhost:6379", {
  maxRetriesPerRequest: 3,
})

// Job queues
const ingestQueue = new Queue("ingest", { connection: redis })
const briefQueue = new Queue("brief", { connection: redis })
const digestQueue = new Queue("digest", { connection: redis })

// Workers
const ingestWorker = new Worker(
  "ingest",
  async (job) => {
    logger.info("Processing ingest job", { jobId: job.id })
    await ingestTEDNotices()
  },
  { connection: redis },
)

const briefWorker = new Worker(
  "brief",
  async (job) => {
    logger.info("Processing brief job", { jobId: job.id })
    await generateBriefs()
  },
  { connection: redis },
)

const digestWorker = new Worker(
  "digest",
  async (job) => {
    logger.info("Processing digest job", { jobId: job.id })
    await sendDigests()
  },
  { connection: redis },
)

// Error handling
const workers = [ingestWorker, briefWorker, digestWorker]
workers.forEach((worker) => {
  worker.on("completed", (job) => {
    logger.info("Job completed", { queue: worker.name, jobId: job.id })
  })

  worker.on("failed", (job, err) => {
    logger.error("Job failed", { queue: worker.name, jobId: job?.id, error: err.message })
  })
})

// Schedule CRON jobs
async function setupCronJobs() {
  // Clear existing repeatable jobs
  await ingestQueue.removeRepeatable("ingest-ted", { pattern: "0 6 * * *" })
  await briefQueue.removeRepeatable("generate-briefs", { pattern: "45 6 * * *" })
  await digestQueue.removeRepeatable("send-digests", { pattern: "30 7 * * *" })

  // Schedule new jobs (Europe/Prague timezone)
  await ingestQueue.add(
    "ingest-ted",
    {},
    {
      repeat: { pattern: "0 6 * * *", tz: "Europe/Prague" },
      jobId: "ingest-ted",
    },
  )

  await briefQueue.add(
    "generate-briefs",
    {},
    {
      repeat: { pattern: "45 6 * * *", tz: "Europe/Prague" },
      jobId: "generate-briefs",
    },
  )

  await digestQueue.add(
    "send-digests",
    {},
    {
      repeat: { pattern: "30 7 * * *", tz: "Europe/Prague" },
      jobId: "send-digests",
    },
  )

  logger.info("CRON jobs scheduled")
}

// Graceful shutdown
process.on("SIGTERM", async () => {
  logger.info("Shutting down workers...")
  await Promise.all([ingestWorker.close(), briefWorker.close(), digestWorker.close()])
  await redis.quit()
  process.exit(0)
})

// Start the worker
async function start() {
  try {
    await setupCronJobs()
    logger.info("Worker started successfully")
  } catch (error) {
    logger.error("Failed to start worker", { error: error.message })
    process.exit(1)
  }
}

start()
