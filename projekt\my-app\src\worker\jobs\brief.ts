import { prisma } from "../../lib/prisma"
import { generateBrief } from "../../lib/ai"
import { logger } from "../../lib/logger"

export async function generateBriefs(): Promise<void> {
  logger.info("Starting AI brief generation")

  try {
    // Find notices without briefs
    const notices = await prisma.notice.findMany({
      where: {
        brief: null,
        publicationDate: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        },
      },
      take: 50, // Process in batches
      orderBy: { publicationDate: "desc" },
    })

    if (notices.length === 0) {
      logger.info("No notices found for brief generation")
      return
    }

    logger.info(`Generating briefs for ${notices.length} notices`)

    let processed = 0
    let errors = 0

    for (const notice of notices) {
      try {
        const briefResult = await generateBrief({
          noticeData: notice.raw,
          language: notice.language || "cs",
        })

        await prisma.brief.create({
          data: {
            noticeId: notice.id,
            summary: briefResult.summary,
            eligibility: briefResult.eligibility,
            blockers: briefResult.blockers,
            confidence: briefResult.confidence,
            model: briefResult.model,
            tokensIn: briefResult.tokensIn,
            tokensOut: briefResult.tokensOut,
            costUsd: briefResult.costUsd,
          },
        })

        processed++
        logger.debug("Brief generated", { noticeId: notice.id, eligibility: briefResult.eligibility })

        // Small delay to avoid rate limits
        await new Promise((resolve) => setTimeout(resolve, 100))
      } catch (error) {
        errors++
        logger.error("Failed to generate brief", {
          noticeId: notice.id,
          error: error.message,
        })

        // Create fallback brief
        await prisma.brief.create({
          data: {
            noticeId: notice.id,
            summary: "Nejsem si jistý: Chyba při generování AI shrnutí. Zkontrolujte prosím originální dokument.",
            eligibility: "UNCLEAR",
            blockers: ["Technická chyba při zpracování"],
            confidence: 0.1,
            model: "fallback",
            tokensIn: 0,
            tokensOut: 0,
            costUsd: 0,
          },
        })
      }
    }

    // Log audit entry
    await prisma.auditLog.create({
      data: {
        level: "info",
        src: "brief-worker",
        message: "Brief generation completed",
        meta: {
          totalNotices: notices.length,
          processed,
          errors,
        },
      },
    })

    logger.info("Brief generation completed", { processed, errors })
  } catch (error) {
    logger.error("Brief generation failed", { error: error.message })

    await prisma.auditLog.create({
      data: {
        level: "error",
        src: "brief-worker",
        message: "Brief generation failed",
        meta: { error: error.message },
      },
    })

    throw error
  }
}
