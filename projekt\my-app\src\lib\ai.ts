import { generateText } from "ai"
import { openai } from "@ai-sdk/openai"
import { logger } from "./logger"
import { createHash } from "crypto"

export interface BriefRequest {
  noticeData: any
  language?: string
}

export interface BriefResponse {
  summary: string
  eligibility: "YES" | "NO" | "UNCLEAR"
  blockers: string[]
  confidence: number
}

export interface BriefMetrics {
  tokensIn: number
  tokensOut: number
  costUsd: number
  model: string
}

const SYSTEM_PROMPT_CS = `Jsi analytik veřejných zakázek. Z poskytnutých dat o zakázce připrav krátký 'bid-brief': co se poptává, kdo <PERSON>ad<PERSON>, term<PERSON><PERSON>, hlavn<PERSON> kvalif<PERSON>, klíčová rizika a doporučení. Pokud si nejsi jistý, jasně to označ.`

const SYSTEM_PROMPT_EN = `You are a public procurement analyst. From the provided tender data, prepare a short 'bid-brief': what is being procured, who is the buyer, deadlines, main qualification requirements, key risks and recommendations. If you're unsure, clearly mark it.`

function getUserPrompt(noticeData: any, language = "cs"): string {
  const isCzech = language === "cs"

  const template = isCzech
    ? `
Vstup: ${JSON.stringify(noticeData, null, 2)}

Úkol:
Shrň v 8–12 řádcích: (a) zadavatel, (b) předmět, (c) CPV, (d) deadline, (e) místo plnění, (f) kvalifikační požadavky, (g) hodnoticí kritéria, (h) forma/typ řízení, (i) jazyk nabídky.

Eligibility = ANO/NE/NEJISTÉ + 1–3 bullet důvody.
Red flags: max 3 (např. extrémně krátká lhůta, specifická reference, vendor-lock).

Pokud chybí data, napiš "Nejsem si jistý: …" a nepřidávej vymyšlená fakta.

Výstup: JSON
{
  "summary": "…",
  "eligibility": "YES|NO|UNCLEAR",
  "blockers": ["…","…"],
  "confidence": 0.0-1.0
}
`
    : `
Input: ${JSON.stringify(noticeData, null, 2)}

Task:
Summarize in 8-12 lines: (a) buyer, (b) subject, (c) CPV, (d) deadline, (e) place of performance, (f) qualification requirements, (g) evaluation criteria, (h) procedure type, (i) bid language.

Eligibility = YES/NO/UNCLEAR + 1-3 bullet reasons.
Red flags: max 3 (e.g. extremely short deadline, specific references, vendor-lock).

If data is missing, write "I'm not sure: …" and don't add made-up facts.

Output: JSON
{
  "summary": "…",
  "eligibility": "YES|NO|UNCLEAR",
  "blockers": ["…","…"],
  "confidence": 0.0-1.0
}
`

  return template
}

export async function generateBrief(request: BriefRequest): Promise<BriefResponse & BriefMetrics> {
  const { noticeData, language = "cs" } = request

  // Create cache key
  const cacheKey = createHash("sha256")
    .update(JSON.stringify(noticeData) + language + "v1")
    .digest("hex")

  logger.info("Generating AI brief", { cacheKey, language })

  try {
    const systemPrompt = language === "cs" ? SYSTEM_PROMPT_CS : SYSTEM_PROMPT_EN
    const userPrompt = getUserPrompt(noticeData, language)

    const provider = process.env.AI_PROVIDER || "openai"
    const model = getAIModel(provider)

    const result = await generateText({
      model,
      system: systemPrompt,
      prompt: userPrompt,
      temperature: 0.1,
      maxTokens: 400,
    })

    // Parse JSON response
    let briefData: BriefResponse
    try {
      const jsonMatch = result.text.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error("No JSON found in response")
      }
      briefData = JSON.parse(jsonMatch[0])
    } catch (parseError) {
      logger.warn("Failed to parse AI response as JSON", { response: result.text })
      // Fallback response
      briefData = {
        summary: result.text.substring(0, 500) + "...",
        eligibility: "UNCLEAR",
        blockers: ["Nejsem si jistý: Chyba při zpracování AI odpovědi"],
        confidence: 0.3,
      }
    }

    // Calculate metrics (approximate)
    const tokensIn = Math.ceil((systemPrompt + userPrompt).length / 4)
    const tokensOut = Math.ceil(result.text.length / 4)
    const costUsd = calculateCost(provider, tokensIn, tokensOut)

    return {
      ...briefData,
      tokensIn,
      tokensOut,
      costUsd,
      model: `${provider}-${model}`,
    }
  } catch (error) {
    logger.error("AI brief generation failed", { error: error.message, cacheKey })

    // Return fallback response
    return {
      summary: "Nejsem si jistý: Chyba při generování AI shrnutí. Zkontrolujte prosím originální dokument.",
      eligibility: "UNCLEAR",
      blockers: ["Technická chyba při zpracování"],
      confidence: 0.1,
      tokensIn: 0,
      tokensOut: 0,
      costUsd: 0,
      model: "fallback",
    }
  }
}

function getAIModel(provider: string) {
  switch (provider) {
    case "openai":
      return openai("gpt-4o-mini") // Cost-effective model
    case "azure":
      return openai("gpt-4o-mini") // Adjust for Azure
    default:
      return openai("gpt-4o-mini")
  }
}

function calculateCost(provider: string, tokensIn: number, tokensOut: number): number {
  // Approximate costs (USD per 1K tokens)
  const costs = {
    openai: { input: 0.00015, output: 0.0006 }, // GPT-4o-mini
    azure: { input: 0.00015, output: 0.0006 },
  }

  const cost = costs[provider as keyof typeof costs] || costs.openai
  return (tokensIn * cost.input + tokensOut * cost.output) / 1000
}
