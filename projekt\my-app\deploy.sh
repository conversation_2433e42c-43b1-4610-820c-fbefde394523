#!/bin/bash

echo "🚀 RFP Radar - Produk<PERSON><PERSON><PERSON> na<PERSON>"
echo "=================================="

# Kontrola Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker není nainstalován. Stáhněte si ho z https://www.docker.com/products/docker-desktop/"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose není dostupný"
    exit 1
fi

echo "✅ Docker je dostupný"

# Kontrola .env.production
if [ ! -f ".env.production" ]; then
    echo "❌ Soubor .env.production neexistuje!"
    echo "📝 Zkopírujte .env.production.example a upravte hodnoty"
    exit 1
fi

echo "✅ Produkční konfigurace nalezena"

# Build a spuštění
echo "🔨 Buildování aplikace..."
if command -v docker-compose &> /dev/null; then
    docker-compose -f docker-compose.prod.yml down
    docker-compose -f docker-compose.prod.yml build --no-cache
    docker-compose -f docker-compose.prod.yml up -d
else
    docker compose -f docker-compose.prod.yml down
    docker compose -f docker-compose.prod.yml build --no-cache
    docker compose -f docker-compose.prod.yml up -d
fi

echo ""
echo "🎉 Aplikace je nasazená!"
echo "📍 URL: http://localhost:3000"
echo "📊 Logy: docker-compose -f docker-compose.prod.yml logs -f"
echo "🛑 Stop: docker-compose -f docker-compose.prod.yml down"
echo ""
echo "⚠️  DŮLEŽITÉ:"
echo "   1. Změňte NEXTAUTH_SECRET v .env.production"
echo "   2. Nastavte skutečné API klíče (OpenAI, Postmark)"
echo "   3. Změňte NEXT_PUBLIC_APP_URL na vaši doménu"
