# 🚀 RFP Radar - Produkční nasazení

## Možnosti nasazení

### 1. 🐳 Docker Compose (Doporučeno pro VPS/lokální server)

**Požadavky:**
- Docker Desktop nebo Docker Engine
- 2GB RAM minimum
- 10GB volného místa

**Kroky:**
1. **Upravte produkční konfiguraci:**
   ```bash
   cp .env.production.example .env.production
   # Upravte .env.production - změňte všechny hodnoty!
   ```

2. **Spusťte deployment:**
   ```bash
   ./deploy.sh
   ```

3. **Aplikace bude dostupná na:** http://localhost:3000

**Správa:**
```bash
# Zobrazit logy
docker-compose -f docker-compose.prod.yml logs -f

# Restartovat
docker-compose -f docker-compose.prod.yml restart

# Zastavit
docker-compose -f docker-compose.prod.yml down

# Aktualizovat
git pull
./deploy.sh
```

### 2. ☁️ Vercel + Supabase + Upstash (Nejjednodušší cloud)

**Požadavky:**
- Vercel účet (zdarma)
- Supabase účet (PostgreSQL zdarma)
- Upstash účet (Redis zdarma)

**Kroky:**
1. **Vytvořte databázi na Supabase:**
   - Jděte na https://supabase.com
   - Vytvořte nový projekt
   - Zkopírujte DATABASE_URL

2. **Vytvořte Redis na Upstash:**
   - Jděte na https://upstash.com
   - Vytvořte Redis databázi
   - Zkopírujte REDIS_URL

3. **Nasaďte na Vercel:**
   ```bash
   npm i -g vercel
   vercel login
   vercel --prod
   ```

4. **Nastavte environment variables ve Vercel:**
   - DATABASE_URL
   - REDIS_URL
   - NEXTAUTH_SECRET
   - OPENAI_API_KEY
   - POSTMARK_TOKEN

### 3. 🚂 Railway (Jednoduchý cloud)

**Kroky:**
1. **Připojte GitHub repo k Railway:**
   - Jděte na https://railway.app
   - Připojte GitHub repository

2. **Přidejte služby:**
   - PostgreSQL addon
   - Redis addon

3. **Nastavte environment variables**

4. **Deploy se spustí automaticky**

### 4. 🌊 DigitalOcean App Platform

**Kroky:**
1. **Vytvořte app na DigitalOcean:**
   - Připojte GitHub repo
   - Vyberte Node.js

2. **Přidejte databáze:**
   - PostgreSQL managed database
   - Redis managed database

3. **Nastavte environment variables**

## 🔧 Důležité nastavení pro produkci

### Environment Variables
```env
# POVINNÉ - změňte v produkci!
NEXTAUTH_SECRET=your-very-secure-secret-key
NEXT_PUBLIC_APP_URL=https://your-domain.com
DATABASE_URL=postgresql://...
REDIS_URL=redis://...

# API klíče
OPENAI_API_KEY=sk-...
POSTMARK_TOKEN=...

# Volitelné
BETA_LIMIT_WATCHLISTS=2
MAX_ITEMS_PER_DIGEST=10
APP_TIMEZONE=Europe/Prague
```

### SSL/HTTPS
- **Vercel/Railway:** Automatické HTTPS
- **VPS:** Použijte Nginx + Let's Encrypt
- **Docker:** Přidejte reverse proxy (Traefik/Nginx)

### Monitoring
- Health check: `/api/health`
- Logy: strukturované JSON (Pino)
- Metriky: počet uživatelů, AI usage, email delivery

### Backup
- **Databáze:** Automatické zálohy (managed services)
- **VPS:** Nastavte cron job pro pg_dump

## 💰 Náklady (odhad měsíčně)

### Vercel + Supabase + Upstash
- **Vercel:** $0 (Hobby) / $20 (Pro)
- **Supabase:** $0 (Free) / $25 (Pro)
- **Upstash:** $0 (Free) / $10 (Pay-as-you-go)
- **OpenAI:** $10-50 (závisí na usage)
- **Postmark:** $1-15 (závisí na počtu emailů)
- **Celkem:** $11-120/měsíc

### VPS (DigitalOcean/Hetzner)
- **Server:** $5-20/měsíc
- **Managed PostgreSQL:** $15-50/měsíc
- **Managed Redis:** $10-30/měsíc
- **API služby:** $11-65/měsíc
- **Celkem:** $41-165/měsíc

### Railway
- **Aplikace:** $5-20/měsíc
- **PostgreSQL:** $5-15/měsíc
- **Redis:** $5-10/měsíc
- **API služby:** $11-65/měsíc
- **Celkem:** $26-110/měsíc

## 🔒 Bezpečnost

- [ ] Změňte všechny default hesla
- [ ] Použijte silný NEXTAUTH_SECRET
- [ ] Nastavte HTTPS
- [ ] Omezte přístup k databázi
- [ ] Pravidelně aktualizujte závislosti
- [ ] Monitorujte logy na podezřelou aktivitu

## 🆘 Troubleshooting

### Časté problémy:
1. **Build selhává:** Zkontrolujte Node.js verzi (18+)
2. **Databáze nedostupná:** Ověřte DATABASE_URL
3. **AI nefunguje:** Zkontrolujte OPENAI_API_KEY
4. **Emaily se neodesílají:** Ověřte POSTMARK_TOKEN

### Užitečné příkazy:
```bash
# Logy aplikace
docker-compose -f docker-compose.prod.yml logs web

# Logy workeru
docker-compose -f docker-compose.prod.yml logs worker

# Databázové migrace
docker-compose -f docker-compose.prod.yml exec web npx prisma migrate deploy

# Restart služby
docker-compose -f docker-compose.prod.yml restart web
```
