import { prisma } from "../../lib/prisma"
import { sendDigest } from "../../lib/email"
import { logger } from "../../lib/logger"
import { format } from "date-fns"
import { cs } from "date-fns/locale"

export async function sendDigests(): Promise<void> {
  logger.info("Starting digest sending")

  try {
    // Get users with active watchlists
    const users = await prisma.user.findMany({
      where: {
        watchlists: {
          some: { isActive: true },
        },
      },
      include: {
        watchlists: {
          where: { isActive: true },
          include: {
            matches: {
              where: {
                createdAt: {
                  gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
                },
              },
              include: {
                notice: {
                  include: { brief: true },
                },
              },
              orderBy: { relevanceScore: "desc" },
              take: Number.parseInt(process.env.MAX_ITEMS_PER_DIGEST || "10"),
            },
          },
        },
      },
    })

    if (users.length === 0) {
      logger.info("No users found for digest sending")
      return
    }

    logger.info(`Sending digests to ${users.length} users`)

    let sent = 0
    let errors = 0

    for (const user of users) {
      try {
        // Collect all matches from all watchlists
        const allMatches = user.watchlists.flatMap((w) => w.matches)

        if (allMatches.length === 0) {
          logger.debug("No matches found for user, skipping digest", { userId: user.id })
          continue
        }

        // Sort by relevance score and take top items
        const topMatches = allMatches
          .sort((a, b) => b.relevanceScore - a.relevanceScore)
          .slice(0, Number.parseInt(process.env.MAX_ITEMS_PER_DIGEST || "10"))

        // Create digest record
        const digest = await prisma.digest.create({
          data: {
            userId: user.id,
            sentAt: new Date(),
            subject: `[RFP Radar] Dnešní výběr zakázek (${format(new Date(), "dd.MM.yyyy", { locale: cs })})`,
          },
        })

        // Create digest items
        const digestItems = await Promise.all(
          topMatches.map((match, index) =>
            prisma.digestItem.create({
              data: {
                digestId: digest.id,
                noticeId: match.notice.id,
                position: index,
              },
            }),
          ),
        )

        // Prepare email data
        const emailData = {
          to: user.email,
          subject: digest.subject,
          watchlistName: user.watchlists.map((w) => w.name).join(", "),
          digestId: digest.id,
          items: topMatches.map((match) => ({
            id: match.notice.id,
            title: match.notice.title,
            buyer: match.notice.buyerName || "Neuvedeno",
            deadline: match.notice.deadline,
            eligibility: match.notice.brief?.eligibility || "UNCLEAR",
            summary: match.notice.brief?.summary || "Shrnutí není k dispozici.",
            url: match.notice.url,
          })),
        }

        // Send email
        await sendDigest(emailData)
        sent++

        logger.debug("Digest sent", {
          userId: user.id,
          digestId: digest.id,
          itemCount: topMatches.length,
        })
      } catch (error) {
        errors++
        logger.error("Failed to send digest", {
          userId: user.id,
          error: error.message,
        })
      }
    }

    // Log audit entry
    await prisma.auditLog.create({
      data: {
        level: "info",
        src: "digest-worker",
        message: "Digest sending completed",
        meta: {
          totalUsers: users.length,
          sent,
          errors,
        },
      },
    })

    logger.info("Digest sending completed", { sent, errors })
  } catch (error) {
    logger.error("Digest sending failed", { error: error.message })

    await prisma.auditLog.create({
      data: {
        level: "error",
        src: "digest-worker",
        message: "Digest sending failed",
        meta: { error: error.message },
      },
    })

    throw error
  }
}
