# Environment
NODE_ENV=production

# Authentication
NEXTAUTH_SECRET=your-very-secure-secret-key-change-this

# App URL
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/rfp_radar

# Redis
REDIS_URL=redis://localhost:6379

# Email (Postmark)
POSTMARK_TOKEN=your-postmark-server-token

# AI Provider
AI_PROVIDER=openai
OPENAI_API_KEY=your-openai-api-key

# Alternative AI providers
# AI_PROVIDER=azure
# AZURE_OPENAI_API_KEY=your-azure-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com

# AI_PROVIDER=openrouter
# OPENROUTER_API_KEY=your-openrouter-key

# Feature flags
BETA_LIMIT_WATCHLISTS=2
MAX_ITEMS_PER_DIGEST=10

# Timezone
APP_TIMEZONE=Europe/Prague
DIGEST_LOCAL_TIME=07:30

# Logging
LOG_LEVEL=info
