import { Client } from "postmark"
import { logger } from "./logger"
import { format } from "date-fns"
import { cs } from "date-fns/locale"

const postmark = new Client(process.env.POSTMARK_TOKEN || "test-token")

export interface DigestEmail {
  to: string
  subject: string
  watchlistName: string
  items: DigestItem[]
  digestId: string
}

export interface DigestItem {
  id: string
  title: string
  buyer: string
  deadline?: Date
  eligibility: "YES" | "NO" | "UNCLEAR"
  summary: string
  url: string
}

export async function sendMagicLink(email: string, token: string): Promise<void> {
  const loginUrl = `${process.env.NEXT_PUBLIC_APP_URL}/auth/verify?token=${token}`

  try {
    await postmark.sendEmail({
      From: "<EMAIL>",
      To: email,
      Subject: "Přihlášení do RFP Radar",
      HtmlBody: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2><PERSON><PERSON><PERSON>lášení do RFP Radar</h2>
          <p>Klikněte na odkaz níže pro přihlášení do vaší aplikace:</p>
          <p>
            <a href="${loginUrl}" style="background: #0066cc; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              Přihlásit se
            </a>
          </p>
          <p><small>Odkaz je platný 15 minut.</small></p>
          <hr>
          <p><small>Pokud jste tento e-mail neočekávali, můžete ho ignorovat.</small></p>
        </div>
      `,
      TextBody: `
        Přihlášení do RFP Radar
        
        Klikněte na odkaz níže pro přihlášení:
        ${loginUrl}
        
        Odkaz je platný 15 minut.
      `,
    })

    logger.info("Magic link sent", { email })
  } catch (error) {
    logger.error("Failed to send magic link", { email, error: error.message })
    throw error
  }
}

export async function sendDigest(digest: DigestEmail): Promise<void> {
  const trackingPixelUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/tracking/open?digestId=${digest.digestId}`

  const eligibilityBadge = (status: string) => {
    const colors = {
      YES: "#22c55e",
      NO: "#ef4444",
      UNCLEAR: "#f59e0b",
    }
    return `<span style="background: ${colors[status as keyof typeof colors] || colors.UNCLEAR}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">${status}</span>`
  }

  const itemsHtml = digest.items
    .map(
      (item, index) => `
    <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
      <h3 style="margin: 0 0 8px 0; color: #1f2937;">
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/notices/${item.id}?utm_source=digest&utm_medium=email&utm_campaign=daily&digestId=${digest.digestId}&position=${index}" 
           style="color: #1f2937; text-decoration: none;">
          ${item.title}
        </a>
      </h3>
      <p style="margin: 4px 0; color: #6b7280; font-size: 14px;">
        <strong>Zadavatel:</strong> ${item.buyer} | 
        <strong>Deadline:</strong> ${item.deadline ? format(item.deadline, "dd.MM.yyyy", { locale: cs }) : "Neuvedeno"} |
        ${eligibilityBadge(item.eligibility)}
      </p>
      <p style="margin: 8px 0 0 0; color: #374151; font-size: 14px; line-height: 1.4;">
        ${item.summary.substring(0, 200)}${item.summary.length > 200 ? "..." : ""}
      </p>
      <p style="margin: 12px 0 0 0;">
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/notices/${item.id}?utm_source=digest&utm_medium=email&utm_campaign=daily&digestId=${digest.digestId}&position=${index}" 
           style="background: #0066cc; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 14px;">
          Zobrazit detail
        </a>
      </p>
    </div>
  `,
    )
    .join("")

  const htmlBody = `
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; background: #ffffff;">
      <div style="background: #0066cc; color: white; padding: 24px; text-align: center;">
        <h1 style="margin: 0; font-size: 24px;">RFP Radar</h1>
        <p style="margin: 8px 0 0 0; opacity: 0.9;">Dnešní výběr zakázek</p>
      </div>
      
      <div style="padding: 24px;">
        <h2 style="color: #1f2937; margin: 0 0 16px 0;">
          ${digest.watchlistName} (${format(new Date(), "dd.MM.yyyy", { locale: cs })})
        </h2>
        
        ${itemsHtml}
        
        <div style="border-top: 1px solid #e5e7eb; padding-top: 16px; margin-top: 24px; text-align: center; color: #6b7280; font-size: 12px;">
          <p>
            <em>AI shrnutí, ověřte v originálu.</em> | 
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard" style="color: #0066cc;">Spravovat watchlisty</a>
          </p>
          <p>RFP Radar - Monitoring veřejných zakázek</p>
        </div>
      </div>
      
      <img src="${trackingPixelUrl}" width="1" height="1" style="display: none;" alt="">
    </div>
  `

  try {
    await postmark.sendEmail({
      From: "<EMAIL>",
      To: digest.to,
      Subject: digest.subject,
      HtmlBody: htmlBody,
      TextBody: `
        ${digest.subject}
        
        ${digest.items
          .map(
            (item, index) => `
        ${index + 1}. ${item.title}
        Zadavatel: ${item.buyer}
        Deadline: ${item.deadline ? format(item.deadline, "dd.MM.yyyy", { locale: cs }) : "Neuvedeno"}
        Eligibility: ${item.eligibility}
        
        ${item.summary}
        
        Detail: ${process.env.NEXT_PUBLIC_APP_URL}/notices/${item.id}
        
        ---
        `,
          )
          .join("")}
        
        AI shrnutí, ověřte v originálu.
        Spravovat watchlisty: ${process.env.NEXT_PUBLIC_APP_URL}/dashboard
      `,
      Tag: "digest",
      TrackOpens: true,
      TrackLinks: "HtmlAndText",
    })

    logger.info("Digest sent", { to: digest.to, digestId: digest.digestId, itemCount: digest.items.length })
  } catch (error) {
    logger.error("Failed to send digest", { to: digest.to, error: error.message })
    throw error
  }
}
