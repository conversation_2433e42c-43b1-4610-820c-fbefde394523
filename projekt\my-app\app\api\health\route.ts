import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import IORedis from "ioredis"

export async function GET() {
  try {
    // Check database
    await prisma.$queryRaw`SELECT 1`

    // Check Redis
    const redis = new IORedis(process.env.REDIS_URL || "redis://localhost:6379")
    await redis.ping()
    await redis.quit()

    return NextResponse.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      services: {
        database: "ok",
        redis: "ok",
      },
    })
  } catch (error) {
    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: error.message,
      },
      { status: 500 },
    )
  }
}
