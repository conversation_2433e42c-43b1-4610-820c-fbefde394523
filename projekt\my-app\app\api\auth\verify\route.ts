import { type NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyMagicToken, generateSessionToken } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { logger } from "@/lib/logger"

const requestSchema = z.object({
  token: z.string(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token } = requestSchema.parse(body)

    // Verify magic link token
    const decoded = verifyMagicToken(token)
    if (!decoded) {
      return NextResponse.json({ error: "Neplatný nebo vypr<PERSON>elý token" }, { status: 400 })
    }

    // Find or create user
    let user = await prisma.user.findUnique({
      where: { email: decoded.email },
    })

    if (!user) {
      user = await prisma.user.create({
        data: { email: decoded.email },
      })
      logger.info("New user created", { userId: user.id, email: user.email })
    }

    // Generate session token
    const sessionToken = generateSessionToken(user.id)

    logger.info("User authenticated", { userId: user.id, email: user.email })

    return NextResponse.json({
      success: true,
      sessionToken,
      user: { id: user.id, email: user.email },
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Neplatné data" }, { status: 400 })
    }

    logger.error("Authentication failed", { error: error.message })

    return NextResponse.json({ error: "Něco se pokazilo" }, { status: 500 })
  }
}
