import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Eye } from "lucide-react"

// Mock data
const mockWatchlists = [
  {
    id: "1",
    name: "IT Services & Software",
    keywords: ["software", "IT services", "development", "digital"],
    cpvCodes: ["72000000", "48000000"],
    countries: ["CZ", "SK", "DE"],
    languages: ["cs", "en", "de"],
    isActive: true,
    matchCount: 15,
    recentMatches: [
      {
        id: "1",
        title: "Software Development Services for Digital Transformation",
        buyer: "Ministry of Digital Affairs",
        publicationDate: "2024-11-01",
      },
      {
        id: "2",
        title: "Cloud Infrastructure Services",
        buyer: "Prague City Council",
        publicationDate: "2024-10-28",
      },
    ],
  },
  {
    id: "2",
    name: "Construction & Infrastructure",
    keywords: ["construction", "infrastructure", "building"],
    cpvCodes: ["45000000"],
    countries: ["CZ"],
    languages: ["cs"],
    isActive: true,
    matchCount: 8,
    recentMatches: [
      {
        id: "3",
        title: "Road Infrastructure Modernization",
        buyer: "Czech Road Authority",
        publicationDate: "2024-10-30",
      },
    ],
  },
]

const mockDigests = [
  {
    id: "1",
    subject: "[RFP Radar] Dnešní výběr zakázek (01.11.2024)",
    sentAt: "2024-11-01T07:30:00Z",
    opened: true,
    itemCount: 5,
    items: [
      { title: "Software Development Services for Digital Transformation" },
      { title: "Cloud Infrastructure Services" },
    ],
  },
  {
    id: "2",
    subject: "[RFP Radar] Dnešní výběr zakázek (31.10.2024)",
    sentAt: "2024-10-31T07:30:00Z",
    opened: true,
    itemCount: 3,
    items: [{ title: "Road Infrastructure Modernization" }, { title: "Healthcare IT System Upgrade" }],
  },
  {
    id: "3",
    subject: "[RFP Radar] Dnešní výběr zakázek (30.10.2024)",
    sentAt: "2024-10-30T07:30:00Z",
    opened: false,
    itemCount: 7,
    items: [{ title: "Municipal Waste Management System" }, { title: "Educational Software Platform" }],
  },
]

export default function DashboardPage() {
  const totalMatches = mockWatchlists.reduce((sum, w) => sum + w.matchCount, 0)
  const totalDigests = mockDigests.length
  const openRate = ((mockDigests.filter((d) => d.opened).length / mockDigests.length) * 100).toFixed(1)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Search className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold">RFP Radar</span>
          </Link>

          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600"><EMAIL></span>
            <Link href="/">
              <Button variant="outline" size="sm">
                Zpět na hlavní stránku
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Welcome */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Demo Dashboard</h1>
          <p className="text-gray-600">Přehled vašich watchlistů a posledních nalezených zakázek</p>
        </div>

        {/* Stats */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Aktivní watchlisty</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockWatchlists.filter((w) => w.isActive).length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Celkem nalezeno</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalMatches}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Odesláno digestů</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalDigests}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Míra otevření</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{openRate}%</div>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Watchlists */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold">Vaše watchlisty</h2>
              <Link href="/watchlists/new">
                <Button size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Nový watchlist
                </Button>
              </Link>
            </div>

            <div className="space-y-4">
              {mockWatchlists.map((watchlist) => (
                <Card key={watchlist.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{watchlist.name}</CardTitle>
                      <div className="flex items-center space-x-2">
                        <Badge variant={watchlist.isActive ? "default" : "secondary"}>
                          {watchlist.isActive ? "Aktivní" : "Neaktivní"}
                        </Badge>
                        <Badge variant="outline">{watchlist.matchCount} nalezeno</Badge>
                      </div>
                    </div>
                    <CardDescription>
                      {watchlist.keywords.slice(0, 3).join(", ")}
                      {watchlist.keywords.length > 3 && ` +${watchlist.keywords.length - 3} dalších`}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {watchlist.recentMatches.length > 0 ? (
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700">Poslední nálezy:</p>
                        {watchlist.recentMatches.map((match) => (
                          <div key={match.id} className="text-sm">
                            <Link href={`/notices/${match.id}`} className="text-blue-600 hover:underline">
                              {match.title}
                            </Link>
                            <div className="text-gray-500 text-xs">
                              {match.buyer} • {new Date(match.publicationDate).toLocaleDateString("cs-CZ")}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">Zatím žádné nálezy</p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Recent Digests */}
          <div>
            <h2 className="text-xl font-semibold mb-6">Poslední digesty</h2>

            <div className="space-y-4">
              {mockDigests.map((digest) => (
                <Card key={digest.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{digest.subject}</CardTitle>
                      <div className="flex items-center space-x-2">
                        {digest.opened ? (
                          <Badge variant="outline" className="text-green-600">
                            <Eye className="w-3 h-3 mr-1" />
                            Otevřeno
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-gray-500">
                            Neotevřeno
                          </Badge>
                        )}
                        <Badge variant="secondary">{digest.itemCount} položek</Badge>
                      </div>
                    </div>
                    <CardDescription>
                      Odesláno {new Date(digest.sentAt).toLocaleDateString("cs-CZ")} v{" "}
                      {new Date(digest.sentAt).toLocaleTimeString("cs-CZ", { hour: "2-digit", minute: "2-digit" })}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {digest.items.length > 0 && (
                      <div className="space-y-1">
                        {digest.items.slice(0, 2).map((item, index) => (
                          <div key={index} className="text-sm">
                            <span className="text-blue-600">{item.title}</span>
                          </div>
                        ))}
                        {digest.items.length > 2 && (
                          <p className="text-xs text-gray-500">+{digest.items.length - 2} dalších položek</p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Demo Notice */}
        <div className="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <Search className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">Toto je demo verze</h3>
              <p className="text-blue-800 text-sm mb-4">
                Zobrazená data jsou pouze ukázková. V produkční verzi by aplikace automaticky stahovala skutečné zakázky
                z TED API, generovala AI shrnutí a odesílala denní e-mailové digesty.
              </p>
              <div className="flex flex-wrap gap-2">
                <Link href="/watchlists/new">
                  <Button size="sm" variant="outline" className="bg-white">
                    Vytvořit watchlist
                  </Button>
                </Link>
                <Link href="/notices/demo">
                  <Button size="sm" variant="outline" className="bg-white">
                    Zobrazit ukázkovou zakázku
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
