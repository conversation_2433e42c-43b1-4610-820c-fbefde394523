import Link from "next/link"
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Search, Building, Calendar, MapPin } from "lucide-react"

// <PERSON>ck notices data
const mockNotices = [
  {
    id: "1",
    title: "Software Development Services for Digital Transformation",
    buyer: "Ministry of Digital Affairs",
    country: "CZ",
    language: "cs",
    cpvMain: "72000000",
    deadline: "2024-12-15T23:59:59Z",
    publicationDate: "2024-11-01T08:00:00Z",
    eligibility: "YES" as const,
    relevanceScore: 0.92,
  },
  {
    id: "2",
    title: "Cloud Infrastructure Services",
    buyer: "Prague City Council",
    country: "CZ",
    language: "cs",
    cpvMain: "72000000",
    deadline: "2024-11-30T23:59:59Z",
    publicationDate: "2024-10-28T08:00:00Z",
    eligibility: "YES" as const,
    relevanceScore: 0.87,
  },
  {
    id: "3",
    title: "Road Infrastructure Modernization",
    buyer: "Czech Road Authority",
    country: "CZ",
    language: "cs",
    cpvMain: "45000000",
    deadline: "2024-12-20T23:59:59Z",
    publicationDate: "2024-10-30T08:00:00Z",
    eligibility: "UNCLEAR" as const,
    relevanceScore: 0.65,
  },
  {
    id: "4",
    title: "Healthcare IT System Upgrade",
    buyer: "Ministry of Health",
    country: "CZ",
    language: "cs",
    cpvMain: "72000000",
    deadline: "2024-11-25T23:59:59Z",
    publicationDate: "2024-10-25T08:00:00Z",
    eligibility: "NO" as const,
    relevanceScore: 0.45,
  },
]

export default function NoticesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/dashboard" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Search className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold">RFP Radar</span>
          </Link>

          <div className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                Zpět na Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Nalezené zakázky</h1>
          <p className="text-gray-600">Přehled všech zakázek odpovídajících vašim watchlistům</p>
        </div>

        {/* Filters */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Filtry</CardTitle>
            <CardDescription>Upřesněte výsledky podle vašich potřeb</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Eligibility</label>
                <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                  <option value="">Všechny</option>
                  <option value="YES">ANO</option>
                  <option value="NO">NE</option>
                  <option value="UNCLEAR">NEJISTÉ</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">CPV kód</label>
                <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                  <option value="">Všechny</option>
                  <option value="72000000">72000000 - IT služby</option>
                  <option value="45000000">45000000 - Stavební práce</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Země</label>
                <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                  <option value="">Všechny</option>
                  <option value="CZ">Česká republika</option>
                  <option value="SK">Slovensko</option>
                  <option value="DE">Německo</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Seřadit podle</label>
                <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                  <option value="relevance">Relevance</option>
                  <option value="date">Datum publikace</option>
                  <option value="deadline">Deadline</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        <div className="space-y-6">
          {mockNotices.map((notice) => (
            <Card key={notice.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-xl mb-2">
                      <Link href={`/notices/${notice.id}`} className="text-blue-600 hover:underline">
                        {notice.title}
                      </Link>
                    </CardTitle>
                    <div className="flex items-center space-x-4 text-gray-600 text-sm">
                      <div className="flex items-center space-x-1">
                        <Building className="w-4 h-4" />
                        <span>{notice.buyer}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{notice.country}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>Publikováno: {new Date(notice.publicationDate).toLocaleDateString("cs-CZ")}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end space-y-2">
                    <Badge
                      variant={
                        notice.eligibility === "YES"
                          ? "default"
                          : notice.eligibility === "NO"
                            ? "destructive"
                            : "secondary"
                      }
                      className={notice.eligibility === "YES" ? "bg-green-100 text-green-800" : ""}
                    >
                      {notice.eligibility === "YES" ? "ANO" : notice.eligibility === "NO" ? "NE" : "NEJISTÉ"}
                    </Badge>
                    <div className="text-xs text-gray-500">Relevance: {(notice.relevanceScore * 100).toFixed(0)}%</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-gray-600">CPV kód</div>
                    <div className="font-semibold">{notice.cpvMain}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Deadline</div>
                    <div className="font-semibold text-red-600">
                      {new Date(notice.deadline).toLocaleDateString("cs-CZ")}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Zbývá dní</div>
                    <div className="font-semibold">
                      {Math.ceil((new Date(notice.deadline).getTime() - Date.now()) / (1000 * 60 * 60 * 24))}
                    </div>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    Nalezeno ve watchlistu: <span className="font-medium">IT Services & Software</span>
                  </div>
                  <Link href={`/notices/${notice.id}`}>
                    <Button size="sm">Zobrazit detail</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Pagination */}
        <div className="mt-8 flex justify-center">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" disabled>
              Předchozí
            </Button>
            <Button variant="outline" size="sm" className="bg-blue-600 text-white">
              1
            </Button>
            <Button variant="outline" size="sm">
              2
            </Button>
            <Button variant="outline" size="sm">
              3
            </Button>
            <Button variant="outline" size="sm">
              Další
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
