# RFP Radar - Monitoring <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zakázek

Micro-SaaS pro monitoring a AI shrnutí veřejných zakázek z EU TED databáze s denními e-mailovými digesty.

## 🚀 Rychlý start

### Lokální vývoj

1. **Klonování a instalace**
\`\`\`bash
git clone <repository-url>
cd rfp-radar
npm install
\`\`\`

2. **Nastavení prostředí**
\`\`\`bash
cp .env.example .env
# Upravte .env soubor s vašimi hodnotami
\`\`\`

3. **Spuštění databáze a Redis**
\`\`\`bash
docker-compose up postgres redis -d
\`\`\`

4. **Migrace databáze**
\`\`\`bash
npm run db:migrate
npm run db:seed
\`\`\`

5. **Spuštění aplikace**
\`\`\`bash
# Web aplikace
npm run dev

# Worker (v novém terminálu)
npm run worker
\`\`\`

Aplikace bude dostupná na `http://localhost:3000`

### Produkční nasazení

1. **Docker Compose (doporučeno)**
\`\`\`bash
# Upravte docker-compose.yml s produkčními hodnotami
docker-compose up -d
\`\`\`

2. **Jednotlivé kontejnery**
\`\`\`bash
# Build images
docker build -t rfp-radar-web .
docker build -f Dockerfile.worker -t rfp-radar-worker .

# Run containers
docker run -d --name rfp-radar-web -p 3000:3000 rfp-radar-web
docker run -d --name rfp-radar-worker rfp-radar-worker
\`\`\`

## 📋 Funkce

### ✅ Implementované
- **Autentizace**: Magic-link přihlášení bez hesla
- **Watchlisty**: Definice klíčových slov, CPV kódů, zemí a jazyků
- **TED API integrace**: Denní stahování nových zakázek
- **AI shrnutí**: Automatické generování bid-briefů s eligibility hodnocením
- **E-mail digesty**: Denní přehledy v 7:30 Europe/Prague
- **Scoring**: TF-IDF + CPV matching + recency bonus
- **Tracking**: Open rate a click tracking pro digesty
- **Admin API**: Manuální spuštění jobů
- **Health check**: Monitoring stavu aplikace

### 🔄 CRON Jobs
- **06:00** - Ingest nových zakázek z TED API
- **06:45** - Generování AI briefů pro nové zakázky  
- **07:30** - Odesílání denních digestů

## 🏗️ Architektura

\`\`\`
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Next.js   │    │   Worker    │    │  PostgreSQL │
│   Web App   │◄──►│   BullMQ    │◄──►│  Database   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Redis    │    │  TED API    │    │  Postmark   │
│   Queue     │    │  Search     │    │   Email     │
└─────────────┘    └─────────────┘    └─────────────┘
\`\`\`

## 🔧 API Endpoints

### Autentizace
- `POST /api/auth/magic-link` - Odeslání magic linku
- `POST /api/auth/verify` - Ověření magic linku

### Uživatel
- `GET /api/me` - Profil uživatele

### Watchlisty
- `GET /api/watchlists` - Seznam watchlistů
- `POST /api/watchlists` - Vytvoření watchlistu
- `PATCH /api/watchlists/:id` - Úprava watchlistu
- `DELETE /api/watchlists/:id` - Smazání watchlistu

### Zakázky
- `GET /api/notices/:id` - Detail zakázky s AI briefem

### Admin (pouze pro administrátory)
- `POST /api/ingest/run` - Manuální spuštění ingestu
- `POST /api/digest/send` - Manuální odeslání digestů

### Monitoring
- `GET /api/health` - Health check
- `GET /api/tracking/open` - Tracking otevření digestu

## 🧪 Testování

\`\`\`bash
# Unit testy
npm test

# E2E testy
npm run test:e2e

# Performance testy (k6)
k6 run tests/performance/load-test.js
\`\`\`

## 📊 Monitoring

### Health Check
\`\`\`bash
curl http://localhost:3000/api/health
\`\`\`

### Logy
Aplikace používá strukturované logování (Pino):
\`\`\`bash
# Sledování logů
docker-compose logs -f web worker
\`\`\`

### Metriky
- Počet zpracovaných zakázek
- AI token usage a náklady
- E-mail delivery rate
- Open rate digestů

## 🔒 Bezpečnost

- **HTTPS**: Povinné v produkci
- **Rate limiting**: Implementováno na API endpoints
- **GDPR**: Minimální ukládání osobních údajů
- **AI transparentnost**: Všechna AI shrnutí jsou označena

## 💰 Náklady (odhad)

### Měsíční náklady pro 100 uživatelů:
- **Hosting**: $20-50 (VPS/Cloud)
- **Database**: $10-25 (managed PostgreSQL)
- **Redis**: $5-15 (managed Redis)
- **AI (OpenAI)**: $10-30 (závisí na objemu)
- **Email (Postmark)**: $1-10 (závisí na počtu digestů)

**Celkem**: ~$50-130/měsíc

## 🚀 Nasazení na cloud platformy

### Vercel + Supabase + Upstash
\`\`\`bash
# Vercel deployment
vercel --prod

# Environment variables
vercel env add DATABASE_URL
vercel env add REDIS_URL
vercel env add POSTMARK_TOKEN
vercel env add OPENAI_API_KEY
\`\`\`

### Railway
\`\`\`bash
railway login
railway init
railway up
\`\`\`

### Fly.io
\`\`\`bash
fly launch
fly deploy
\`\`\`

## 📝 Konfigurace

### AI Provider
\`\`\`env
# OpenAI (default)
AI_PROVIDER=openai
OPENAI_API_KEY=sk-...

# Azure OpenAI
AI_PROVIDER=azure
AZURE_OPENAI_API_KEY=...
AZURE_OPENAI_ENDPOINT=https://...

# OpenRouter
AI_PROVIDER=openrouter
OPENROUTER_API_KEY=...
\`\`\`

### Feature Flags
\`\`\`env
BETA_LIMIT_WATCHLISTS=2        # Max watchlistů v beta
MAX_ITEMS_PER_DIGEST=10        # Max položek v digestu
DIGEST_LOCAL_TIME=07:30        # Čas odesílání digestů
\`\`\`

## 🐛 Troubleshooting

### Časté problémy

1. **TED API nedostupné**
   - Aplikace vrací mock data v development módu
   - Zkontrolujte logy pro chybové hlášky

2. **E-maily se neodesílají**
   - Ověřte POSTMARK_TOKEN
   - Zkontrolujte spam složku

3. **AI generování selhává**
   - Zkontrolujte OPENAI_API_KEY
   - Aplikace vrací fallback odpovědi při chybách

4. **Worker se nespouští**
   - Ověřte REDIS_URL připojení
   - Zkontrolujte logy workeru

### Logy a debugging
\`\`\`bash
# Detailní logy
LOG_LEVEL=debug npm run dev

# Worker logy
docker-compose logs -f worker

# Database logy
docker-compose logs -f postgres
\`\`\`

## 📚 Dokumentace

- [API Reference](./docs/api.md)
- [Database Schema](./docs/database.md)
- [Deployment Guide](./docs/deployment.md)
- [Architecture Overview](./docs/architecture.md)

## 🤝 Přispívání

1. Fork repository
2. Vytvořte feature branch (`git checkout -b feature/amazing-feature`)
3. Commit změny (`git commit -m 'Add amazing feature'`)
4. Push do branch (`git push origin feature/amazing-feature`)
5. Otevřete Pull Request

## 📄 Licence

MIT License - viz [LICENSE](LICENSE) soubor.

## 📞 Podpora

- **Issues**: GitHub Issues
- **Email**: <EMAIL>
- **Dokumentace**: [docs.rfp-radar.com](https://docs.rfp-radar.com)

---

**RFP Radar** - Inteligentní monitoring veřejných zakázek 🎯
