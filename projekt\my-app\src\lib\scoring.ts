import { logger } from "./logger"

export interface ScoringParams {
  noticeTitle: string
  noticeDescription?: string
  noticeCpv?: string
  noticeCountry?: string
  noticeLanguage?: string
  publicationDate: Date
  watchlistKeywords: string[]
  watchlistCpvCodes: string[]
  watchlistCountries: string[]
  watchlistLanguages: string[]
}

export function calculateRelevanceScore(params: ScoringParams): number {
  let score = 0
  let maxScore = 0

  // Keyword matching (TF-IDF simplified) - 40% weight
  const keywordScore = calculateKeywordScore(
    params.noticeTitle + " " + (params.noticeDescription || ""),
    params.watchlistKeywords,
  )
  score += keywordScore * 0.4
  maxScore += 0.4

  // CPV code matching - 30% weight
  const cpvScore = calculateCpvScore(params.noticeCpv, params.watchlistCpvCodes)
  score += cpvScore * 0.3
  maxScore += 0.3

  // Country matching - 15% weight
  const countryScore = calculateCountryScore(params.noticeCountry, params.watchlistCountries)
  score += countryScore * 0.15
  maxScore += 0.15

  // Language matching - 10% weight
  const languageScore = calculateLanguageScore(params.noticeLanguage, params.watchlistLanguages)
  score += languageScore * 0.1
  maxScore += 0.1

  // Recency bonus - 5% weight
  const recencyScore = calculateRecencyScore(params.publicationDate)
  score += recencyScore * 0.05
  maxScore += 0.05

  // Normalize to 0-1 range
  const normalizedScore = maxScore > 0 ? score / maxScore : 0

  logger.debug("Relevance score calculated", {
    keywordScore,
    cpvScore,
    countryScore,
    languageScore,
    recencyScore,
    finalScore: normalizedScore,
  })

  return Math.min(1, Math.max(0, normalizedScore))
}

function calculateKeywordScore(text: string, keywords: string[]): number {
  if (!keywords.length || !text) return 0

  const normalizedText = text.toLowerCase()
  let matches = 0

  for (const keyword of keywords) {
    const normalizedKeyword = keyword.toLowerCase()
    if (normalizedText.includes(normalizedKeyword)) {
      matches++
    }
  }

  return keywords.length > 0 ? matches / keywords.length : 0
}

function calculateCpvScore(noticeCpv: string | undefined, watchlistCpvCodes: string[]): number {
  if (!noticeCpv || !watchlistCpvCodes.length) return 0

  for (const watchlistCpv of watchlistCpvCodes) {
    // Exact match
    if (noticeCpv === watchlistCpv) return 1

    // Prefix match (e.g., 72000000 matches 72****)
    if (noticeCpv.startsWith(watchlistCpv.replace(/\*+$/, ""))) return 0.8
    if (watchlistCpv.startsWith(noticeCpv)) return 0.8

    // Same category (first 2 digits)
    if (noticeCpv.substring(0, 2) === watchlistCpv.substring(0, 2)) return 0.3
  }

  return 0
}

function calculateCountryScore(noticeCountry: string | undefined, watchlistCountries: string[]): number {
  if (!noticeCountry || !watchlistCountries.length) return 0.5 // Neutral if not specified

  return watchlistCountries.includes(noticeCountry.toUpperCase()) ? 1 : 0
}

function calculateLanguageScore(noticeLanguage: string | undefined, watchlistLanguages: string[]): number {
  if (!noticeLanguage || !watchlistLanguages.length) return 0.5 // Neutral if not specified

  return watchlistLanguages.includes(noticeLanguage.toLowerCase()) ? 1 : 0
}

function calculateRecencyScore(publicationDate: Date): number {
  const now = new Date()
  const daysDiff = (now.getTime() - publicationDate.getTime()) / (1000 * 60 * 60 * 24)

  // Full score for notices published today, decreasing over 30 days
  if (daysDiff <= 1) return 1
  if (daysDiff <= 7) return 0.8
  if (daysDiff <= 14) return 0.6
  if (daysDiff <= 30) return 0.4
  return 0.2
}
