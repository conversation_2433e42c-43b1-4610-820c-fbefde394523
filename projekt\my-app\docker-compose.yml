version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: rfp_radar
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/rfp_radar
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_SECRET=your-secret-key-change-in-production
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - POSTMARK_TOKEN=your-postmark-token
      - AI_PROVIDER=openai
      - OPENAI_API_KEY=your-openai-api-key
      - BETA_LIMIT_WATCHLISTS=2
      - MAX_ITEMS_PER_DIGEST=10
      - APP_TIMEZONE=Europe/Prague
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: >
      sh -c "npx prisma migrate deploy &&
             npx prisma db seed &&
             node server.js"

  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/rfp_radar
      - REDIS_URL=redis://redis:6379
      - POSTMARK_TOKEN=your-postmark-token
      - AI_PROVIDER=openai
      - OPENAI_API_KEY=your-openai-api-key
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - APP_TIMEZONE=Europe/Prague
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
