"use client"

import type React from "react"
import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Search, Plus, X } from "lucide-react"

export default function NewWatchlistPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: "",
    keywords: [] as string[],
    cpvCodes: [] as string[],
    countries: [] as string[],
    languages: [] as string[],
    minScore: 0.3,
  })
  const [currentKeyword, setCurrentKeyword] = useState("")
  const [currentCpv, setCurrentCpv] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const addKeyword = () => {
    if (currentKeyword.trim() && !formData.keywords.includes(currentKeyword.trim())) {
      setFormData((prev) => ({
        ...prev,
        keywords: [...prev.keywords, currentKeyword.trim()],
      }))
      setCurrentKeyword("")
    }
  }

  const removeKeyword = (keyword: string) => {
    setFormData((prev) => ({
      ...prev,
      keywords: prev.keywords.filter((k) => k !== keyword),
    }))
  }

  const addCpv = () => {
    if (currentCpv.trim() && !formData.cpvCodes.includes(currentCpv.trim())) {
      setFormData((prev) => ({
        ...prev,
        cpvCodes: [...prev.cpvCodes, currentCpv.trim()],
      }))
      setCurrentCpv("")
    }
  }

  const removeCpv = (cpv: string) => {
    setFormData((prev) => ({
      ...prev,
      cpvCodes: prev.cpvCodes.filter((c) => c !== cpv),
    }))
  }

  const toggleCountry = (country: string) => {
    setFormData((prev) => ({
      ...prev,
      countries: prev.countries.includes(country)
        ? prev.countries.filter((c) => c !== country)
        : [...prev.countries, country],
    }))
  }

  const toggleLanguage = (language: string) => {
    setFormData((prev) => ({
      ...prev,
      languages: prev.languages.includes(language)
        ? prev.languages.filter((l) => l !== language)
        : [...prev.languages, language],
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    if (!formData.name.trim()) {
      setError("Název watchlistu je povinný")
      setIsLoading(false)
      return
    }

    if (formData.keywords.length === 0) {
      setError("Alespoň jedno klíčové slovo je povinné")
      setIsLoading(false)
      return
    }

    // Simulate API call
    setTimeout(() => {
      console.log("Creating watchlist:", formData)
      router.push("/dashboard")
    }, 1000)
  }

  const commonCpvCodes = [
    { code: "72000000", name: "IT služby: konzultace, vývoj softwaru, internet a podpora" },
    { code: "48000000", name: "Softwarové balíčky a informační systémy" },
    { code: "45000000", name: "Stavební práce" },
    { code: "50000000", name: "Opravy a údržba" },
    { code: "79000000", name: "Obchodní služby: právo, marketing, konzultace, nábor, tisk a bezpečnost" },
  ]

  const countries = [
    { code: "CZ", name: "Česká republika" },
    { code: "SK", name: "Slovensko" },
    { code: "DE", name: "Německo" },
    { code: "AT", name: "Rakousko" },
    { code: "PL", name: "Polsko" },
  ]

  const languages = [
    { code: "cs", name: "Čeština" },
    { code: "en", name: "Angličtina" },
    { code: "de", name: "Němčina" },
    { code: "sk", name: "Slovenština" },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/dashboard" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Search className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold">RFP Radar</span>
          </Link>

          <div className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                Zpět na Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Nový Watchlist</h1>
          <p className="text-gray-600">Definujte kritéria pro sledování relevantních veřejných zakázek</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle>Základní informace</CardTitle>
              <CardDescription>Název a popis vašeho watchlistu</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Název watchlistu *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                  placeholder="např. IT služby pro veřejný sektor"
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Keywords */}
          <Card>
            <CardHeader>
              <CardTitle>Klíčová slova *</CardTitle>
              <CardDescription>Slova, která se budou vyhledávat v názvech a popisech zakázek</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={currentKeyword}
                  onChange={(e) => setCurrentKeyword(e.target.value)}
                  placeholder="Zadejte klíčové slovo"
                  onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addKeyword())}
                />
                <Button type="button" onClick={addKeyword}>
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              {formData.keywords.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.keywords.map((keyword) => (
                    <Badge key={keyword} variant="secondary" className="flex items-center gap-1">
                      {keyword}
                      <button
                        type="button"
                        onClick={() => removeKeyword(keyword)}
                        className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* CPV Codes */}
          <Card>
            <CardHeader>
              <CardTitle>CPV kódy</CardTitle>
              <CardDescription>Klasifikace předmětu veřejné zakázky (Common Procurement Vocabulary)</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={currentCpv}
                  onChange={(e) => setCurrentCpv(e.target.value)}
                  placeholder="např. 72000000"
                  onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addCpv())}
                />
                <Button type="button" onClick={addCpv}>
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700">Často používané CPV kódy:</p>
                <div className="grid gap-2">
                  {commonCpvCodes.map((cpv) => (
                    <button
                      key={cpv.code}
                      type="button"
                      onClick={() => {
                        if (!formData.cpvCodes.includes(cpv.code)) {
                          setFormData((prev) => ({
                            ...prev,
                            cpvCodes: [...prev.cpvCodes, cpv.code],
                          }))
                        }
                      }}
                      className="text-left p-2 text-sm border rounded hover:bg-gray-50 disabled:opacity-50"
                      disabled={formData.cpvCodes.includes(cpv.code)}
                    >
                      <span className="font-mono text-blue-600">{cpv.code}</span> - {cpv.name}
                    </button>
                  ))}
                </div>
              </div>

              {formData.cpvCodes.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.cpvCodes.map((cpv) => (
                    <Badge key={cpv} variant="secondary" className="flex items-center gap-1">
                      {cpv}
                      <button
                        type="button"
                        onClick={() => removeCpv(cpv)}
                        className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Countries */}
          <Card>
            <CardHeader>
              <CardTitle>Země</CardTitle>
              <CardDescription>Vyberte země, ze kterých chcete sledovat zakázky</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {countries.map((country) => (
                  <button
                    key={country.code}
                    type="button"
                    onClick={() => toggleCountry(country.code)}
                    className={`p-2 text-sm border rounded text-left transition-colors ${
                      formData.countries.includes(country.code)
                        ? "bg-blue-50 border-blue-300 text-blue-700"
                        : "hover:bg-gray-50"
                    }`}
                  >
                    <span className="font-mono text-xs">{country.code}</span> {country.name}
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Languages */}
          <Card>
            <CardHeader>
              <CardTitle>Jazyky</CardTitle>
              <CardDescription>Jazyky, ve kterých jsou zakázky publikovány</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {languages.map((language) => (
                  <button
                    key={language.code}
                    type="button"
                    onClick={() => toggleLanguage(language.code)}
                    className={`p-2 text-sm border rounded text-left transition-colors ${
                      formData.languages.includes(language.code)
                        ? "bg-blue-50 border-blue-300 text-blue-700"
                        : "hover:bg-gray-50"
                    }`}
                  >
                    <span className="font-mono text-xs">{language.code}</span> {language.name}
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Min Score */}
          <Card>
            <CardHeader>
              <CardTitle>Minimální skóre relevance</CardTitle>
              <CardDescription>Pouze zakázky s vyšším skóre budou zahrnuty do digestů (0.0 - 1.0)</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Input
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  value={formData.minScore}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, minScore: Number.parseFloat(e.target.value) || 0 }))
                  }
                />
                <p className="text-xs text-gray-500">Doporučeno: 0.3 pro široké sledování, 0.6 pro přesné výsledky</p>
              </div>
            </CardContent>
          </Card>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Submit */}
          <div className="flex gap-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Vytvářím...
                </>
              ) : (
                "Vytvořit Watchlist"
              )}
            </Button>
            <Link href="/dashboard">
              <Button type="button" variant="outline">
                Zrušit
              </Button>
            </Link>
          </div>
        </form>
      </div>
    </div>
  )
}
