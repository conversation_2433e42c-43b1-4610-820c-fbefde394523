# PRODUKČNÍ KONFIGURACE
# DŮLEŽITÉ: Změňte všechny hodnoty před nasazením!

# Environment
NODE_ENV=production

# Authentication - ZMĚŇTE NA SILNÉ HESLO!
NEXTAUTH_SECRET=your-very-secure-secret-key-change-this-in-production-123456789

# App URL - ZMĚŇTE NA VAŠI DOMÉNU!
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Database (Docker PostgreSQL)
DATABASE_URL=********************************************/rfp_radar

# Redis (Docker Redis)
REDIS_URL=redis://redis:6379

# Email (Postmark) - ZÍSKEJTE SKUTEČNÝ TOKEN!
POSTMARK_TOKEN=your-postmark-server-token

# AI Provider - ZÍSKEJTE SKUTEČNÝ API KLÍČ!
AI_PROVIDER=openai
OPENAI_API_KEY=your-openai-api-key

# Feature flags
BETA_LIMIT_WATCHLISTS=2
MAX_ITEMS_PER_DIGEST=10

# Timezone
APP_TIMEZONE=Europe/Prague
DIGEST_LOCAL_TIME=07:30

# Logging
LOG_LEVEL=info
