import { type NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { generateMagicToken } from "@/lib/auth"
import { sendMagicLink } from "@/lib/email"
import { logger } from "@/lib/logger"

const requestSchema = z.object({
  email: z.string().email("Neplatný e-mail"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = requestSchema.parse(body)

    // Generate magic link token
    const token = generateMagicToken(email)

    // Send magic link email
    await sendMagicLink(email, token)

    logger.info("Magic link requested", { email })

    return NextResponse.json({ success: true })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors[0].message }, { status: 400 })
    }

    logger.error("Magic link request failed", { error: error.message })

    return NextResponse.json({ error: "Něco se pokazilo" }, { status: 500 })
  }
}
