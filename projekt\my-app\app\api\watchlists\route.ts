import { type NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { getCurrentUser } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { logger } from "@/lib/logger"

const createWatchlistSchema = z.object({
  name: z.string().min(1, "Název je povinný").max(100, "Název je <PERSON>"),
  keywords: z.array(z.string()).min(1, "Alespoň jedno klíčové slovo je povinné"),
  cpvCodes: z.array(z.string()),
  countries: z.array(z.string()),
  languages: z.array(z.string()),
  minScore: z.number().min(0).max(1).default(0),
})

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session")?.value
    const user = await getCurrentUser(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const watchlists = await prisma.watchlist.findMany({
      where: { userId: user.id },
      include: {
        _count: {
          select: { matches: true },
        },
      },
      orderBy: { createdAt: "desc" },
    })

    return NextResponse.json(watchlists)
  } catch (error) {
    logger.error("Failed to fetch watchlists", { error: error.message })
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session")?.value
    const user = await getCurrentUser(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check beta limit
    const existingCount = await prisma.watchlist.count({
      where: { userId: user.id },
    })

    const betaLimit = Number.parseInt(process.env.BETA_LIMIT_WATCHLISTS || "2")
    if (existingCount >= betaLimit) {
      return NextResponse.json({ error: `V beta verzi můžete mít maximálně ${betaLimit} watchlisty` }, { status: 400 })
    }

    const body = await request.json()
    const data = createWatchlistSchema.parse(body)

    const watchlist = await prisma.watchlist.create({
      data: {
        ...data,
        userId: user.id,
      },
      include: {
        _count: {
          select: { matches: true },
        },
      },
    })

    logger.info("Watchlist created", {
      watchlistId: watchlist.id,
      userId: user.id,
      name: watchlist.name,
    })

    return NextResponse.json(watchlist, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors[0].message }, { status: 400 })
    }

    logger.error("Failed to create watchlist", { error: error.message })
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
