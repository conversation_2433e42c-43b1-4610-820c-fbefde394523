import { PrismaClient } from "@prisma/client"
import { createHash } from "crypto"

const prisma = new PrismaClient()

async function main() {
  console.log("🌱 Seeding database...")

  // Create demo user
  const demoUser = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      timezone: "Europe/Prague",
    },
  })

  // Create demo watchlist
  const demoWatchlist = await prisma.watchlist.upsert({
    where: { id: "demo-watchlist" },
    update: {},
    create: {
      id: "demo-watchlist",
      userId: demoUser.id,
      name: "IT Services & Software",
      keywords: ["software", "IT services", "development", "digital"],
      cpvCodes: ["72000000", "48000000"],
      countries: ["CZ", "SK", "DE"],
      languages: ["cs", "en", "de"],
      minScore: 0.3,
    },
  })

  // Create sample notices
  const sampleNotices = [
    {
      noticeUid: "TED-2024-001",
      title: "Software Development Services for Digital Transformation",
      buyerName: "Ministry of Digital Affairs",
      country: "CZ",
      language: "cs",
      cpvMain: "72000000",
      deadline: new Date("2024-12-31"),
      publicationDate: new Date("2024-11-01"),
      url: "https://ted.europa.eu/udl?uri=TED:NOTICE:001-2024",
      raw: {
        notice_id: "TED-2024-001",
        title: "Software Development Services for Digital Transformation",
        buyer: "Ministry of Digital Affairs",
        description: "Comprehensive software development services for government digital transformation initiative.",
      },
    },
    {
      noticeUid: "TED-2024-002",
      title: "Cloud Infrastructure Services",
      buyerName: "Prague City Council",
      country: "CZ",
      language: "cs",
      cpvMain: "72000000",
      deadline: new Date("2024-11-30"),
      publicationDate: new Date("2024-10-15"),
      url: "https://ted.europa.eu/udl?uri=TED:NOTICE:002-2024",
      raw: {
        notice_id: "TED-2024-002",
        title: "Cloud Infrastructure Services",
        buyer: "Prague City Council",
        description: "Cloud infrastructure and hosting services for municipal systems.",
      },
    },
  ]

  for (const notice of sampleNotices) {
    const hash = createHash("sha256").update(JSON.stringify(notice.raw)).digest("hex")

    await prisma.notice.upsert({
      where: { noticeUid: notice.noticeUid },
      update: {},
      create: {
        ...notice,
        hash,
      },
    })
  }

  // Create sample matches
  const notices = await prisma.notice.findMany()
  for (const notice of notices) {
    await prisma.match.upsert({
      where: {
        watchlistId_noticeId: {
          watchlistId: demoWatchlist.id,
          noticeId: notice.id,
        },
      },
      update: {},
      create: {
        watchlistId: demoWatchlist.id,
        noticeId: notice.id,
        relevanceScore: Math.random() * 0.5 + 0.5, // 0.5-1.0
      },
    })
  }

  console.log("✅ Database seeded successfully!")
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
