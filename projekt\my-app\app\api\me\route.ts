import { type NextRequest, NextResponse } from "next/server"
import { getCurrentUser } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session")?.value
    const user = await getCurrentUser(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user with watchlists
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        watchlists: {
          include: {
            _count: {
              select: { matches: true },
            },
          },
        },
      },
    })

    return NextResponse.json(userData)
  } catch (error) {
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
