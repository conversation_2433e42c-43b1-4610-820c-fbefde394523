generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  createdAt DateTime @default(now())
  timezone  String   @default("Europe/Prague")

  watchlists Watchlist[]
  digests    Digest[]

  @@map("users")
}

model Watchlist {
  id        String   @id @default(cuid())
  userId    String
  name      String
  keywords  String[]
  cpvCodes  String[]
  countries String[]
  languages String[]
  minScore  Float    @default(0)
  createdAt DateTime @default(now())
  isActive  Boolean  @default(true)

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  matches Match[]

  @@map("watchlists")
}

model Notice {
  id              String    @id @default(cuid())
  source          String    @default("TED")
  noticeUid       String    @unique
  raw             Json
  title           String
  buyerName       String?
  country         String?
  language        String?
  cpvMain         String?
  deadline        DateTime?
  publicationDate DateTime
  url             String
  hash            String

  matches     Match[]
  brief       Brief?
  digestItems DigestItem[]

  @@index([publicationDate])
  @@index([cpvMain])
  @@index([country])
  @@index([hash])
  @@map("notices")
}

model Match {
  id             String   @id @default(cuid())
  watchlistId    String
  noticeId       String
  relevanceScore Float
  createdAt      DateTime @default(now())

  watchlist Watchlist @relation(fields: [watchlistId], references: [id], onDelete: Cascade)
  notice    Notice    @relation(fields: [noticeId], references: [id], onDelete: Cascade)

  @@unique([watchlistId, noticeId])
  @@map("matches")
}

enum EligibilityStatus {
  YES
  NO
  UNCLEAR
}

model Brief {
  id          String            @id @default(cuid())
  noticeId    String            @unique
  summary     String
  eligibility EligibilityStatus
  blockers    String[]
  confidence  Float
  model       String
  tokensIn    Int
  tokensOut   Int
  costUsd     Decimal           @db.Decimal(10, 4)
  createdAt   DateTime          @default(now())

  notice Notice @relation(fields: [noticeId], references: [id], onDelete: Cascade)

  @@map("briefs")
}

model Digest {
  id      String    @id @default(cuid())
  userId  String
  sentAt  DateTime
  subject String
  opened  Boolean   @default(false)
  openAt  DateTime?

  user  User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  items DigestItem[]

  @@map("digests")
}

model DigestItem {
  id       String    @id @default(cuid())
  digestId String
  noticeId String
  position Int
  clicked  Boolean   @default(false)
  clickAt  DateTime?

  digest Digest @relation(fields: [digestId], references: [id], onDelete: Cascade)
  notice Notice @relation(fields: [noticeId], references: [id], onDelete: Cascade)

  @@map("digest_items")
}

model JobState {
  id        String   @id @default(cuid())
  key       String   @unique
  value     Json
  updatedAt DateTime @updatedAt

  @@map("job_states")
}

enum LogLevel {
  info
  warn
  error
}

model AuditLog {
  id      String   @id @default(cuid())
  at      DateTime @default(now())
  level   LogLevel
  src     String
  message String
  meta    Json?

  @@index([at])
  @@index([level])
  @@map("audit_logs")
}
